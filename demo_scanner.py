#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示版 Open WebUI 扫描器
用于快速测试和演示功能
作者：Claude 4.0 Sonnet
"""

import requests
import base64
import json
import time
import random
from urllib.parse import urlparse, urljoin
import csv
from typing import List, Dict, Set
import logging
import re
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('demo_scanner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("配置文件 config.json 不存在")
        return None
    except json.JSONDecodeError:
        logger.error("配置文件格式错误")
        return None

class DemoFOFAScanner:
    """演示版 FOFA 搜索器"""
    
    def __init__(self, config: dict):
        fofa_config = config['fofa']
        self.fofa_url = fofa_config['url'].rstrip('/')
        self.api_key = fofa_config['api_key']
        self.delay_min = fofa_config['request_delay_min']
        self.delay_max = fofa_config['request_delay_max']
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config['scan_settings']['user_agent']
        })
    
    def search(self, query: str, size: int = 100) -> List[Dict]:
        """执行 FOFA 搜索（演示版限制结果数量）"""
        try:
            # Base64 编码查询语句
            query_encoded = base64.b64encode(query.encode()).decode()
            
            # 构建 API 请求 URL
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'qbase64': query_encoded,
                'key': self.api_key,
                'size': size,
                'fields': 'host,ip,port,country,city,title'
            }
            
            logger.info(f"执行 FOFA 搜索: {query}")
            response = self.session.get(api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('error'):
                logger.error(f"FOFA API 错误: {data['errmsg']}")
                return []
            
            results = data.get('results', [])
            logger.info(f"找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"FOFA 搜索失败: {e}")
            return []
    
    def demo_search(self) -> Set[str]:
        """演示搜索（只搜索少量数据）"""
        
        # 演示查询列表（只搜索几个小范围）
        queries = [
            'title="open webui" && country="CN" && city="Beijing"',
            'title="open webui" && country="US" && port="3000"',
            'title="open webui" && country="DE"',
            'title="open webui" && port="443"',
            'title="Open WebUI"'  # 大写版本
        ]
        
        all_urls = set()
        
        for i, query in enumerate(queries, 1):
            logger.info(f"执行第 {i}/{len(queries)} 个演示查询")
            results = self.search(query, size=50)  # 每个查询最多50个结果
            
            for result in results:
                if len(result) >= 1:  # 确保有 host 字段
                    host = result[0]
                    if host:
                        # 确保 URL 格式正确
                        if not host.startswith(('http://', 'https://')):
                            all_urls.add(f"https://{host}")
                        else:
                            all_urls.add(host)
            
            # 添加延迟
            time.sleep(random.uniform(self.delay_min, self.delay_max))
        
        logger.info(f"演示搜索完成，总共找到 {len(all_urls)} 个唯一网站")
        return all_urls

class DemoWebTester:
    """演示版网站测试器"""
    
    def __init__(self, config: dict):
        scan_config = config['scan_settings']
        test_config = config['test_account']
        
        self.timeout = scan_config['timeout']
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': scan_config['user_agent']
        })
        self.test_email = test_config['email']
        self.test_password = test_config['password']
    
    def quick_check(self, url: str) -> Dict:
        """快速检查网站基本信息"""
        result = {
            'url': url,
            'accessible': False,
            'has_signup': False,
            'title': '',
            'status_code': 0,
            'error': None
        }
        
        try:
            logger.info(f"快速检查: {url}")
            
            # 访问网站
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            result['status_code'] = response.status_code
            response.raise_for_status()
            
            result['accessible'] = True
            content = response.text.lower()
            
            # 提取页面标题
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', response.text, re.IGNORECASE)
            if title_match:
                result['title'] = title_match.group(1).strip()
            
            # 检查是否有注册功能
            signup_indicators = [
                'sign up', 'signup', 'register', 'registration', '注册',
                'create account', 'join', 'get started'
            ]
            
            for indicator in signup_indicators:
                if indicator in content:
                    result['has_signup'] = True
                    break
            
        except requests.exceptions.Timeout:
            result['error'] = '连接超时'
        except requests.exceptions.ConnectionError:
            result['error'] = '连接失败'
        except requests.exceptions.HTTPError as e:
            result['error'] = f'HTTP错误: {e.response.status_code}'
        except Exception as e:
            result['error'] = f'未知错误: {str(e)}'
        
        return result

def main():
    """演示主函数"""
    logger.info("开始演示版 Open WebUI 扫描")
    
    # 加载配置
    config = load_config()
    if not config:
        logger.error("无法加载配置文件，程序退出")
        return
    
    # 初始化扫描器
    fofa_scanner = DemoFOFAScanner(config)
    web_tester = DemoWebTester(config)
    
    try:
        # 1. 演示搜索
        logger.info("步骤 1: 演示搜索部署了 Open WebUI 的网站")
        demo_urls = fofa_scanner.demo_search()
        
        # 保存演示结果
        with open('demo_sites.txt', 'w', encoding='utf-8') as f:
            for url in sorted(demo_urls):
                f.write(f"{url}\n")
        
        logger.info(f"已保存 {len(demo_urls)} 个演示网站到 demo_sites.txt")
        
        # 2. 快速检查前10个网站
        logger.info("步骤 2: 快速检查网站状态")
        demo_results = []
        
        # 只检查前10个网站作为演示
        test_urls = list(demo_urls)[:10]
        
        for i, url in enumerate(test_urls, 1):
            logger.info(f"检查进度: {i}/{len(test_urls)}")
            
            result = web_tester.quick_check(url)
            demo_results.append(result)
            
            if result['accessible']:
                logger.info(f"✓ 可访问: {url} - {result['title']}")
                if result['has_signup']:
                    logger.info(f"  → 发现注册功能")
            else:
                logger.warning(f"✗ 无法访问: {url} - {result['error']}")
            
            # 添加延迟
            time.sleep(2)
        
        # 3. 保存演示结果
        logger.info("步骤 3: 保存演示结果")
        
        # 保存为 CSV
        with open('demo_results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['URL', '状态码', '可访问', '有注册功能', '标题', '错误信息'])
            
            for result in demo_results:
                writer.writerow([
                    result['url'],
                    result['status_code'],
                    '是' if result['accessible'] else '否',
                    '是' if result['has_signup'] else '否',
                    result['title'],
                    result['error'] or ''
                ])
        
        # 统计结果
        accessible_count = sum(1 for r in demo_results if r['accessible'])
        signup_count = sum(1 for r in demo_results if r['has_signup'])
        
        logger.info("=" * 50)
        logger.info("演示扫描完成！")
        logger.info(f"搜索到网站: {len(demo_urls)} 个")
        logger.info(f"测试网站: {len(demo_results)} 个")
        logger.info(f"可访问网站: {accessible_count} 个")
        logger.info(f"有注册功能: {signup_count} 个")
        logger.info("结果已保存到 demo_sites.txt 和 demo_results.csv")
        logger.info("=" * 50)
        
    except KeyboardInterrupt:
        logger.info("用户中断演示")
    except Exception as e:
        logger.error(f"演示过程中发生错误: {e}")

if __name__ == "__main__":
    main()
