# Open WebUI 安全扫描工具

这是一个用于发现部署了 Open WebUI 但未设置邮箱验证的网站的安全扫描工具。

**作者：Claude 4.0 Sonnet**

## 功能特性

- 🔍 使用 FOFA 搜索引擎全面发现 Open WebUI 部署网站
- 🌍 支持全球范围的地理位置分批搜索
- 🚀 自动检测网站可访问性和注册功能
- 🔐 测试注册端点的安全配置
- 📊 生成详细的扫描报告

## 文件说明

### 核心文件

1. **`fofa_scanner.py`** - 完整版扫描器
   - 包含 Selenium WebDriver 自动化测试
   - 可以实际尝试注册并提取模型列表
   - 功能最全面但需要更多依赖

2. **`simple_scanner.py`** - 简化版扫描器（推荐）
   - 基于 HTTP 请求的轻量级实现
   - 快速检测网站可访问性和注册功能
   - 无需浏览器依赖，运行更稳定

3. **`requirements.txt`** - Python 依赖包列表

## 安装和使用

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置 FOFA 信息

在脚本中确认以下配置：
- FOFA URL: `https://fofa.red`
- API Key: `1v43heo2ie8004lp`

### 3. 运行扫描

#### 使用简化版（推荐）：
```bash
python simple_scanner.py
```

#### 使用完整版：
```bash
python fofa_scanner.py
```

## 输出文件

扫描完成后会生成以下文件：

### 简化版输出
- `all_openwebui_sites.txt` - 所有发现的 Open WebUI 网站
- `accessible_sites.csv` - 可访问的网站详细信息
- `potential_vulnerable_sites.txt` - 潜在可注册的网站列表
- `simple_scanner.log` - 扫描日志

### 完整版输出
- `all_openwebui_sites.txt` - 所有发现的网站
- `vulnerable_sites.txt` - 可注册网站及其模型列表
- `vulnerable_sites.csv` - CSV 格式的结果
- `scanner.log` - 详细扫描日志

## FOFA 搜索策略

为了确保全面覆盖且避免单次查询超过 10000 条限制，采用以下分批策略：

### 地理位置分批
- 中国：按省份/直辖市分批
- 美国：按州分批  
- 欧洲：按国家分批
- 亚洲其他国家：单独查询

### 端口分批
- 常用端口：443, 3000, 5000, 8080, 80
- 其他端口：单独查询

### 标题变体
- "open webui"
- "Open WebUI" 
- "openwebui"
- body 内容搜索

## 安全注意事项

1. **请求频率控制**：脚本内置了随机延迟，避免对目标网站造成压力
2. **测试账号**：使用测试邮箱 `<EMAIL>`，密码 `sk123456`
3. **合法使用**：仅用于安全研究和漏洞通知，不得用于恶意目的
4. **隐私保护**：扫描结果仅用于通知网站管理员改进安全配置

## 技术实现

### FOFA API 集成
- Base64 编码查询语句
- 支持多字段返回：host, ip, port, country, city, title
- 错误处理和重试机制

### 网站检测逻辑
1. **可访问性检查**：HTTP 请求测试
2. **注册功能检测**：页面内容分析
3. **API 端点测试**：常见注册接口探测
4. **结果验证**：多重验证确保准确性

## 故障排除

### 常见问题

1. **FOFA API 错误**
   - 检查 API Key 是否正确
   - 确认账户余额充足
   - 验证网络连接

2. **网站访问失败**
   - 部分网站可能有防护措施
   - 网络超时是正常现象
   - 检查代理设置

3. **依赖安装问题**
   - 使用虚拟环境：`python -m venv venv`
   - 更新 pip：`pip install --upgrade pip`
   - 手动安装：`pip install requests selenium`

## 扩展功能

可以根据需要添加以下功能：

1. **代理支持**：添加代理池避免 IP 封禁
2. **多线程**：并发处理提高扫描速度
3. **数据库存储**：使用数据库管理扫描结果
4. **邮件通知**：自动发送安全提醒邮件
5. **定期扫描**：设置定时任务持续监控

## 免责声明

本工具仅用于安全研究和漏洞通知目的。使用者应当：

- 遵守相关法律法规
- 尊重网站服务条款
- 不得用于恶意攻击
- 负责任地披露安全问题

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址：https://github.com/open-webui/open-webui
- 安全问题：请负责任地报告给项目维护者

---

**开发信息**
- 开发模型：Claude 4.0 Sonnet
- 开发时间：2025年8月
- 版本：v1.0
