#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Open WebUI 采样检测器
从大量网站中随机采样进行深度检测
作者：Claude 4.0 Sonnet
"""

import requests
import json
import time
import random
from urllib.parse import urlparse, urljoin
import csv
from typing import List, Dict, Set
import logging
import re
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sample_tester.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("配置文件 config.json 不存在")
        return None
    except json.JSONDecodeError:
        logger.error("配置文件格式错误")
        return None

def load_all_sites():
    """加载所有网站列表"""
    try:
        with open('all_openwebui_sites.txt', 'r', encoding='utf-8') as f:
            sites = [line.strip() for line in f if line.strip()]
        logger.info(f"加载了 {len(sites)} 个网站")
        return sites
    except FileNotFoundError:
        logger.error("网站列表文件 all_openwebui_sites.txt 不存在")
        return []

class SampleTester:
    """采样测试器"""
    
    def __init__(self, config: dict):
        scan_config = config['scan_settings']
        test_config = config['test_account']
        
        self.timeout = scan_config['timeout']
        self.max_retries = scan_config['max_retries']
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': scan_config['user_agent']
        })
        self.test_email = test_config['email']
        self.test_password = test_config['password']
        self.test_username = test_config['username']
        self.test_name = test_config['name']
    
    def quick_check(self, url: str) -> Dict:
        """快速检查网站基本信息"""
        result = {
            'url': url,
            'accessible': False,
            'has_signup': False,
            'title': '',
            'status_code': 0,
            'server': '',
            'error': None
        }
        
        try:
            logger.info(f"检查: {url}")
            
            # 访问网站
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            result['status_code'] = response.status_code
            result['server'] = response.headers.get('Server', '')
            response.raise_for_status()
            
            result['accessible'] = True
            content = response.text.lower()
            
            # 提取页面标题
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', response.text, re.IGNORECASE)
            if title_match:
                result['title'] = title_match.group(1).strip()
            
            # 检查是否有注册功能
            signup_indicators = [
                'sign up', 'signup', 'register', 'registration', '注册',
                'create account', 'join', 'get started', 'new user'
            ]
            
            for indicator in signup_indicators:
                if indicator in content:
                    result['has_signup'] = True
                    break
            
            # 额外检查：查找注册相关的 URL 或按钮
            signup_patterns = [
                r'href=["\'][^"\']*signup[^"\']*["\']',
                r'href=["\'][^"\']*register[^"\']*["\']',
                r'action=["\'][^"\']*signup[^"\']*["\']',
                r'action=["\'][^"\']*register[^"\']*["\']',
                r'/api/.*signup',
                r'/api/.*register'
            ]
            
            for pattern in signup_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    result['has_signup'] = True
                    break
            
        except requests.exceptions.Timeout:
            result['error'] = '连接超时'
        except requests.exceptions.ConnectionError:
            result['error'] = '连接失败'
        except requests.exceptions.HTTPError as e:
            result['error'] = f'HTTP错误: {e.response.status_code}'
        except Exception as e:
            result['error'] = f'未知错误: {str(e)}'
        
        return result
    
    def test_registration(self, url: str) -> Dict:
        """测试注册功能"""
        result = {
            'url': url,
            'registration_successful': False,
            'registration_response': '',
            'models_found': [],
            'api_endpoints_found': [],
            'error': None
        }
        
        try:
            # 常见的注册 API 端点
            registration_endpoints = [
                '/api/v1/auths/signup',
                '/api/auth/signup',
                '/auth/signup',
                '/signup',
                '/register',
                '/api/register',
                '/api/v1/register',
                '/api/users/register',
                '/api/v1/users/signup'
            ]
            
            for endpoint in registration_endpoints:
                try:
                    reg_url = urljoin(url, endpoint)
                    
                    # 准备注册数据
                    registration_data = {
                        'email': self.test_email,
                        'password': self.test_password,
                        'username': self.test_username,
                        'name': self.test_name
                    }
                    
                    # 尝试注册
                    response = self.session.post(
                        reg_url, 
                        json=registration_data, 
                        timeout=self.timeout,
                        headers={'Content-Type': 'application/json'}
                    )
                    
                    result['api_endpoints_found'].append(endpoint)
                    result['registration_response'] = f"Status: {response.status_code}, Response: {response.text[:200]}"
                    
                    # 检查注册是否成功
                    if response.status_code in [200, 201]:
                        result['registration_successful'] = True
                        logger.info(f"✅ 注册成功: {url} - 端点: {endpoint}")
                        
                        # 尝试提取模型信息
                        models = self._extract_models_from_response(response.text)
                        if models:
                            result['models_found'] = models
                        
                        # 尝试获取更多模型信息
                        additional_models = self._try_get_models(url)
                        if additional_models:
                            result['models_found'].extend(additional_models)
                            result['models_found'] = list(set(result['models_found']))  # 去重
                        
                        break
                        
                    elif response.status_code in [400, 422]:
                        # 检查是否是因为邮箱已存在等可接受的错误
                        response_text = response.text.lower()
                        if any(keyword in response_text for keyword in ['already exists', 'already registered', 'duplicate', 'taken']):
                            result['registration_successful'] = True
                            logger.info(f"✅ 注册功能可用（邮箱已存在）: {url} - 端点: {endpoint}")
                            break
                
                except requests.exceptions.RequestException:
                    continue  # 尝试下一个端点
                
                time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            result['error'] = f'注册测试失败: {str(e)}'
        
        return result
    
    def _extract_models_from_response(self, response_text: str) -> List[str]:
        """从响应中提取模型信息"""
        models = []
        try:
            # 尝试解析JSON响应
            if response_text.strip().startswith('{'):
                data = json.loads(response_text)
                # 查找模型相关字段
                model_fields = ['models', 'available_models', 'model_list', 'llm_models']
                for field in model_fields:
                    if field in data and isinstance(data[field], list):
                        models.extend(data[field])
            
            # 使用正则表达式查找模型名称
            model_patterns = [
                r'gpt-[0-9a-zA-Z\-\.]+',
                r'claude-[0-9a-zA-Z\-\.]+',
                r'llama[0-9a-zA-Z\-\.]*',
                r'gemini[0-9a-zA-Z\-\.]*',
                r'mistral[0-9a-zA-Z\-\.]*',
                r'qwen[0-9a-zA-Z\-\.]*',
                r'yi-[0-9a-zA-Z\-\.]+',
                r'baichuan[0-9a-zA-Z\-\.]*'
            ]
            
            for pattern in model_patterns:
                matches = re.findall(pattern, response_text, re.IGNORECASE)
                models.extend(matches)
        
        except Exception:
            pass
        
        return list(set(models))  # 去重
    
    def _try_get_models(self, url: str) -> List[str]:
        """尝试获取模型列表"""
        models = []
        try:
            # 常见的模型API端点
            model_endpoints = [
                '/api/models',
                '/api/v1/models',
                '/models',
                '/api/ollama/models',
                '/api/openai/models',
                '/api/v1/chat/models'
            ]
            
            for endpoint in model_endpoints:
                try:
                    models_url = urljoin(url, endpoint)
                    response = self.session.get(models_url, timeout=self.timeout)
                    
                    if response.status_code == 200:
                        extracted_models = self._extract_models_from_response(response.text)
                        models.extend(extracted_models)
                
                except:
                    continue
        
        except Exception:
            pass
        
        return list(set(models))  # 去重

def main():
    """主函数"""
    logger.info("开始 Open WebUI 采样检测")
    
    # 加载配置
    config = load_config()
    if not config:
        logger.error("无法加载配置文件，程序退出")
        return
    
    # 加载所有网站
    all_sites = load_all_sites()
    if not all_sites:
        logger.error("无法加载网站列表，程序退出")
        return
    
    # 设置采样参数
    sample_size = min(2000, len(all_sites))  # 采样2000个网站，或全部（如果少于2000）
    logger.info(f"从 {len(all_sites)} 个网站中随机采样 {sample_size} 个进行检测")
    
    # 随机采样
    sample_sites = random.sample(all_sites, sample_size)
    
    # 保存采样列表
    with open('sample_sites.txt', 'w', encoding='utf-8') as f:
        for site in sample_sites:
            f.write(f"{site}\n")
    
    # 初始化测试器
    tester = SampleTester(config)
    
    try:
        # 检测采样网站
        logger.info("开始检测采样网站")
        accessible_sites = []
        vulnerable_sites = []
        
        for i, url in enumerate(sample_sites, 1):
            logger.info(f"检测进度: {i}/{sample_size}")
            
            # 基础检查
            basic_result = tester.quick_check(url)
            
            if basic_result['accessible']:
                accessible_sites.append(basic_result)
                logger.info(f"✅ 可访问: {url} - {basic_result['title']}")
                
                if basic_result['has_signup']:
                    logger.info(f"🔍 发现注册功能，进行深度测试...")
                    # 测试注册功能
                    reg_result = tester.test_registration(url)
                    
                    if reg_result['registration_successful']:
                        site_info = {
                            'url': url,
                            'title': basic_result['title'],
                            'server': basic_result['server'],
                            'registration_successful': True,
                            'models_found': reg_result['models_found'],
                            'api_endpoints': reg_result['api_endpoints_found'],
                            'registration_response': reg_result['registration_response']
                        }
                        vulnerable_sites.append(site_info)
                        
                        logger.info(f"🚨 发现可注册网站: {url}")
                        if reg_result['models_found']:
                            logger.info(f"   🤖 发现模型: {', '.join(reg_result['models_found'])}")
            else:
                logger.warning(f"❌ 无法访问: {url} - {basic_result['error']}")
            
            # 添加延迟避免请求过快
            time.sleep(random.uniform(2, 4))
        
        # 保存结果
        logger.info("保存检测结果")
        
        # 保存可访问网站
        with open('sample_accessible_sites.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['URL', '标题', '状态码', '服务器', '有注册功能', '错误信息'])
            
            for site in accessible_sites:
                writer.writerow([
                    site['url'],
                    site['title'],
                    site['status_code'],
                    site['server'],
                    '是' if site['has_signup'] else '否',
                    site['error'] or ''
                ])
        
        # 保存可注册网站
        with open('sample_vulnerable_sites.txt', 'w', encoding='utf-8') as f:
            f.write("采样检测发现的可注册 Open WebUI 网站\n")
            f.write("=" * 50 + "\n\n")
            
            for site in vulnerable_sites:
                f.write(f"🚨 网站: {site['url']}\n")
                f.write(f"   标题: {site['title']}\n")
                f.write(f"   服务器: {site['server']}\n")
                f.write(f"   可用模型: {', '.join(site['models_found']) if site['models_found'] else '未检测到模型'}\n")
                f.write(f"   API端点: {', '.join(site['api_endpoints'])}\n")
                f.write(f"   注册响应: {site['registration_response'][:100]}...\n")
                f.write("-" * 40 + "\n")
        
        # 统计结果
        accessible_count = len(accessible_sites)
        signup_count = len([s for s in accessible_sites if s['has_signup']])
        vulnerable_count = len(vulnerable_sites)
        models_count = len([s for s in vulnerable_sites if s['models_found']])
        
        logger.info("=" * 60)
        logger.info("🎉 采样检测完成！")
        logger.info(f"📊 采样网站: {sample_size} 个")
        logger.info(f"🌐 可访问网站: {accessible_count} 个 ({accessible_count/sample_size*100:.1f}%)")
        logger.info(f"🔍 有注册功能: {signup_count} 个 ({signup_count/sample_size*100:.1f}%)")
        logger.info(f"🚨 实际可注册: {vulnerable_count} 个 ({vulnerable_count/sample_size*100:.1f}%)")
        logger.info(f"🤖 发现模型信息: {models_count} 个网站")
        logger.info("📁 结果文件:")
        logger.info("   - sample_sites.txt (采样网站列表)")
        logger.info("   - sample_accessible_sites.csv (可访问网站)")
        logger.info("   - sample_vulnerable_sites.txt (可注册网站)")
        logger.info("=" * 60)
        
        # 推算全量数据
        if accessible_count > 0:
            estimated_accessible = int(len(all_sites) * accessible_count / sample_size)
            estimated_vulnerable = int(len(all_sites) * vulnerable_count / sample_size)
            
            logger.info("📈 全量数据推算:")
            logger.info(f"   预计可访问网站: ~{estimated_accessible:,} 个")
            logger.info(f"   预计可注册网站: ~{estimated_vulnerable:,} 个")
            logger.info("=" * 60)
        
    except KeyboardInterrupt:
        logger.info("用户中断检测")
    except Exception as e:
        logger.error(f"检测过程中发生错误: {e}")

if __name__ == "__main__":
    main()
