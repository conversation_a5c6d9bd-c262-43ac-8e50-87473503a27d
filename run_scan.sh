#!/bin/bash

echo "========================================"
echo "Open WebUI 安全扫描工具"
echo "作者：Claude 4.0 Sonnet"
echo "========================================"
echo

# 检查 Python 环境
echo "正在检查 Python 环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到 Python3，请先安装 Python 3.7+"
    exit 1
fi

# 检查依赖包
echo "正在检查依赖包..."
if ! python3 -c "import requests" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误：依赖包安装失败"
        exit 1
    fi
fi

echo
echo "选择扫描模式："
echo "1. 简化版扫描（推荐，快速稳定）"
echo "2. 完整版扫描（功能全面，需要更多时间）"
echo
read -p "请输入选择 (1 或 2): " choice

case $choice in
    1)
        echo
        echo "开始运行简化版扫描..."
        python3 simple_scanner.py
        ;;
    2)
        echo
        echo "开始运行完整版扫描..."
        python3 fofa_scanner.py
        ;;
    *)
        echo "无效选择，默认使用简化版扫描..."
        python3 simple_scanner.py
        ;;
esac

echo
echo "扫描完成！请查看生成的结果文件。"
read -p "按回车键退出..."
