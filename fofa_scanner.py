#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Open WebUI 部署网站扫描器
用于发现部署了 open-webui 但未设置邮箱验证的网站
作者：Claude 4.0 Sonnet
"""

import requests
import base64
import json
import time
import random
from urllib.parse import urlparse
import csv
from typing import List, Dict, Set
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, WebDriverException
import re

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scanner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FOFAScanner:
    """FOFA 搜索器"""
    
    def __init__(self, fofa_url: str, api_key: str):
        self.fofa_url = fofa_url.rstrip('/')
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def search(self, query: str, size: int = 10000) -> List[Dict]:
        """
        执行 FOFA 搜索
        
        Args:
            query: FOFA 搜索语法
            size: 返回结果数量限制
            
        Returns:
            搜索结果列表
        """
        try:
            # Base64 编码查询语句
            query_encoded = base64.b64encode(query.encode()).decode()
            
            # 构建 API 请求 URL
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'qbase64': query_encoded,
                'key': self.api_key,
                'size': size,
                'fields': 'host,ip,port,country,city,title,server'
            }
            
            logger.info(f"执行 FOFA 搜索: {query}")
            response = self.session.get(api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('error'):
                logger.error(f"FOFA API 错误: {data['errmsg']}")
                return []
            
            results = data.get('results', [])
            logger.info(f"找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"FOFA 搜索失败: {e}")
            return []
    
    def batch_search(self) -> Set[str]:
        """
        分批搜索所有部署了 open-webui 的网站
        
        Returns:
            去重后的网站 URL 集合
        """
        # 定义搜索查询列表，确保每个查询结果 ≤ 10000
        queries = [
            # 按国家和城市分批
            'title="open webui" && country="CN" && city!="Beijing" && city!="Shanghai" && city!="Guangzhou" && city!="Shenzhen"',
            'title="open webui" && country="CN" && (city="Beijing" || city="Shanghai")',
            'title="open webui" && country="CN" && (city="Guangzhou" || city="Shenzhen")',
            'title="open webui" && country="US" && (port=443 || port=3000)',
            'title="open webui" && country="US" && port!=443 && port!=3000',
            'title="open webui" && country="DE"',
            'title="open webui" && country="JP"',
            'title="open webui" && country="KR"',
            'title="open webui" && country="GB"',
            'title="open webui" && country="FR"',
            'title="open webui" && country="CA"',
            'title="open webui" && country="AU"',
            'title="open webui" && country="IN"',
            'title="open webui" && country="RU"',
            'title="open webui" && country="BR"',
            # 其他国家
            'title="open webui" && country!="CN" && country!="US" && country!="DE" && country!="JP" && country!="KR" && country!="GB" && country!="FR" && country!="CA" && country!="AU" && country!="IN" && country!="RU" && country!="BR" && (port=443 || port=3000)',
            'title="open webui" && country!="CN" && country!="US" && country!="DE" && country!="JP" && country!="KR" && country!="GB" && country!="FR" && country!="CA" && country!="AU" && country!="IN" && country!="RU" && country!="BR" && port!=443 && port!=3000',
            # 按端口分批
            'title="open webui" && port=5000',
            'title="open webui" && port=8080',
            'title="open webui" && port=80',
            'title="open webui" && port!=443 && port!=3000 && port!=5000 && port!=8080 && port!=80',
        ]
        
        all_urls = set()
        
        for i, query in enumerate(queries, 1):
            logger.info(f"执行第 {i}/{len(queries)} 个查询")
            results = self.search(query)
            
            for result in results:
                if len(result) >= 1:  # 确保有 host 字段
                    host = result[0]
                    if host and host.startswith(('http://', 'https://')):
                        all_urls.add(host)
            
            # 添加延迟避免请求过快
            time.sleep(random.uniform(2, 5))
        
        logger.info(f"总共找到 {len(all_urls)} 个唯一网站")
        return all_urls

class WebUITester:
    """Open WebUI 网站测试器"""
    
    def __init__(self):
        self.setup_driver()
        self.test_email = "<EMAIL>"
        self.test_password = "sk123456"
    
    def setup_driver(self):
        """设置 Chrome WebDriver"""
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.set_page_load_timeout(30)
        except Exception as e:
            logger.error(f"WebDriver 初始化失败: {e}")
            self.driver = None
    
    def test_registration(self, url: str) -> Dict:
        """
        测试网站是否允许免费注册
        
        Args:
            url: 目标网站 URL
            
        Returns:
            测试结果字典
        """
        result = {
            'url': url,
            'can_register': False,
            'models': [],
            'error': None
        }
        
        if not self.driver:
            result['error'] = 'WebDriver 未初始化'
            return result
        
        try:
            logger.info(f"测试网站: {url}")
            
            # 访问网站
            self.driver.get(url)
            time.sleep(3)
            
            # 查找注册相关元素
            register_selectors = [
                "//a[contains(text(), 'Sign up')]",
                "//a[contains(text(), 'Register')]", 
                "//a[contains(text(), '注册')]",
                "//button[contains(text(), 'Sign up')]",
                "//button[contains(text(), 'Register')]",
                "//button[contains(text(), '注册')]",
                "//a[@href*='signup']",
                "//a[@href*='register']"
            ]
            
            register_element = None
            for selector in register_selectors:
                try:
                    register_element = self.driver.find_element(By.XPATH, selector)
                    if register_element.is_displayed():
                        break
                except:
                    continue
            
            if not register_element:
                result['error'] = '未找到注册入口'
                return result
            
            # 点击注册
            register_element.click()
            time.sleep(2)
            
            # 尝试填写注册表单
            success = self._fill_registration_form()
            if success:
                result['can_register'] = True
                # 如果注册成功，提取模型列表
                result['models'] = self._extract_models()
            
        except TimeoutException:
            result['error'] = '页面加载超时'
        except WebDriverException as e:
            result['error'] = f'WebDriver 错误: {str(e)}'
        except Exception as e:
            result['error'] = f'未知错误: {str(e)}'
        
        return result
    
    def _fill_registration_form(self) -> bool:
        """填写注册表单"""
        try:
            # 常见的表单字段选择器
            email_selectors = [
                "input[type='email']",
                "input[name*='email']",
                "input[id*='email']",
                "input[placeholder*='email']"
            ]
            
            password_selectors = [
                "input[type='password']",
                "input[name*='password']", 
                "input[id*='password']"
            ]
            
            # 填写邮箱
            email_input = None
            for selector in email_selectors:
                try:
                    email_input = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if email_input.is_displayed():
                        break
                except:
                    continue
            
            if not email_input:
                return False
            
            email_input.clear()
            email_input.send_keys(self.test_email)
            
            # 填写密码
            password_inputs = self.driver.find_elements(By.CSS_SELECTOR, "input[type='password']")
            if len(password_inputs) >= 1:
                password_inputs[0].clear()
                password_inputs[0].send_keys(self.test_password)
                
                # 如果有确认密码字段
                if len(password_inputs) >= 2:
                    password_inputs[1].clear()
                    password_inputs[1].send_keys(self.test_password)
            
            # 查找并点击提交按钮
            submit_selectors = [
                "button[type='submit']",
                "input[type='submit']",
                "//button[contains(text(), 'Sign up')]",
                "//button[contains(text(), 'Register')]",
                "//button[contains(text(), '注册')]"
            ]
            
            for selector in submit_selectors:
                try:
                    if selector.startswith('//'):
                        submit_btn = self.driver.find_element(By.XPATH, selector)
                    else:
                        submit_btn = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if submit_btn.is_displayed():
                        submit_btn.click()
                        time.sleep(3)
                        return True
                except:
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"填写注册表单失败: {e}")
            return False
    
    def _extract_models(self) -> List[str]:
        """提取可用的大模型列表"""
        models = []
        try:
            # 等待页面加载
            time.sleep(5)
            
            # 常见的模型选择器模式
            model_selectors = [
                "//select[@id*='model']//option",
                "//div[contains(@class, 'model')]//text()",
                "//span[contains(text(), 'gpt')]",
                "//span[contains(text(), 'claude')]", 
                "//span[contains(text(), 'llama')]",
                "//span[contains(text(), 'gemini')]",
                "//li[contains(@class, 'model')]",
                "//*[contains(text(), 'gpt-') or contains(text(), 'claude-') or contains(text(), 'llama') or contains(text(), 'gemini')]"
            ]
            
            for selector in model_selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for element in elements:
                        text = element.text.strip()
                        if text and any(keyword in text.lower() for keyword in ['gpt', 'claude', 'llama', 'gemini', 'model']):
                            models.append(text)
                except:
                    continue
            
            # 去重并过滤
            models = list(set(models))
            models = [m for m in models if len(m) > 2 and len(m) < 100]
            
        except Exception as e:
            logger.error(f"提取模型列表失败: {e}")
        
        return models
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()

def main():
    """主函数"""
    logger.info("开始扫描 Open WebUI 部署网站")
    
    # FOFA 配置
    fofa_url = "https://fofa.red"
    api_key = "1v43heo2ie8004lp"
    
    # 初始化扫描器
    fofa_scanner = FOFAScanner(fofa_url, api_key)
    webui_tester = WebUITester()
    
    try:
        # 1. 搜索所有网站
        logger.info("步骤 1: 搜索部署了 Open WebUI 的网站")
        all_urls = fofa_scanner.batch_search()
        
        # 保存所有找到的 URL
        with open('all_openwebui_sites.txt', 'w', encoding='utf-8') as f:
            for url in sorted(all_urls):
                f.write(f"{url}\n")
        
        logger.info(f"已保存 {len(all_urls)} 个网站到 all_openwebui_sites.txt")
        
        # 2. 测试每个网站
        logger.info("步骤 2: 测试网站注册功能")
        vulnerable_sites = []
        
        for i, url in enumerate(all_urls, 1):
            logger.info(f"测试进度: {i}/{len(all_urls)}")
            
            result = webui_tester.test_registration(url)
            
            if result['can_register']:
                vulnerable_sites.append(result)
                logger.info(f"发现可注册网站: {url}")
            elif result['error']:
                logger.warning(f"测试失败 {url}: {result['error']}")
            
            # 添加延迟避免请求过快
            time.sleep(random.uniform(3, 8))
        
        # 3. 保存结果
        logger.info("步骤 3: 保存扫描结果")
        
        # 保存为 TXT 格式
        with open('vulnerable_sites.txt', 'w', encoding='utf-8') as f:
            f.write("可免费注册的 Open WebUI 网站及其可用模型\n")
            f.write("=" * 50 + "\n\n")
            
            for site in vulnerable_sites:
                f.write(f"网站: {site['url']}\n")
                f.write(f"可用模型: {', '.join(site['models']) if site['models'] else '未检测到模型'}\n")
                f.write("-" * 30 + "\n")
        
        # 保存为 CSV 格式
        with open('vulnerable_sites.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['网站URL', '可用模型'])
            
            for site in vulnerable_sites:
                models_str = ', '.join(site['models']) if site['models'] else '未检测到模型'
                writer.writerow([site['url'], models_str])
        
        logger.info(f"扫描完成！发现 {len(vulnerable_sites)} 个可免费注册的网站")
        logger.info("结果已保存到 vulnerable_sites.txt 和 vulnerable_sites.csv")
        
    except KeyboardInterrupt:
        logger.info("用户中断扫描")
    except Exception as e:
        logger.error(f"扫描过程中发生错误: {e}")
    finally:
        webui_tester.close()

if __name__ == "__main__":
    main()
