#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Open WebUI 网站分析器
分析已搜索到的网站数据
作者：Claude 4.0 Sonnet
"""

import re
from urllib.parse import urlparse
from collections import Counter, defaultdict
import csv

def analyze_sites():
    """分析网站数据"""
    print("🔍 分析 Open WebUI 网站数据")
    print("=" * 50)
    
    # 读取网站列表
    try:
        with open('all_openwebui_sites.txt', 'r', encoding='utf-8') as f:
            sites = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        print("❌ 未找到 all_openwebui_sites.txt 文件")
        return
    
    print(f"📊 总网站数量: {len(sites):,}")
    print()
    
    # 分析域名和端口
    domains = []
    ports = []
    protocols = []
    tlds = []
    
    for site in sites:
        try:
            parsed = urlparse(site)
            
            # 协议
            protocols.append(parsed.scheme)
            
            # 域名
            hostname = parsed.hostname or parsed.netloc.split(':')[0]
            domains.append(hostname)
            
            # 端口
            port = parsed.port
            if port:
                ports.append(port)
            elif parsed.scheme == 'https':
                ports.append(443)
            elif parsed.scheme == 'http':
                ports.append(80)
            
            # 顶级域名
            if hostname and '.' in hostname:
                tld = hostname.split('.')[-1]
                tlds.append(tld)
        
        except Exception:
            continue
    
    # 统计协议
    print("🌐 协议分布:")
    protocol_counts = Counter(protocols)
    for protocol, count in protocol_counts.most_common():
        percentage = count / len(sites) * 100
        print(f"   {protocol}: {count:,} ({percentage:.1f}%)")
    print()
    
    # 统计端口
    print("🔌 端口分布 (Top 10):")
    port_counts = Counter(ports)
    for port, count in port_counts.most_common(10):
        percentage = count / len(sites) * 100
        print(f"   {port}: {count:,} ({percentage:.1f}%)")
    print()
    
    # 统计顶级域名
    print("🌍 顶级域名分布 (Top 15):")
    tld_counts = Counter(tlds)
    for tld, count in tld_counts.most_common(15):
        percentage = count / len(sites) * 100
        print(f"   .{tld}: {count:,} ({percentage:.1f}%)")
    print()
    
    # 分析特殊域名模式
    print("🔍 特殊域名模式:")
    
    # IP地址
    ip_pattern = re.compile(r'^\d+\.\d+\.\d+\.\d+$')
    ip_domains = [d for d in domains if d and ip_pattern.match(d)]
    print(f"   IP地址: {len(ip_domains):,} ({len(ip_domains)/len(sites)*100:.1f}%)")
    
    # 本地域名
    local_patterns = ['localhost', '127.0.0.1', '0.0.0.0', '192.168.', '10.', '172.']
    local_domains = [d for d in domains if d and any(pattern in d for pattern in local_patterns)]
    print(f"   本地/内网地址: {len(local_domains):,} ({len(local_domains)/len(sites)*100:.1f}%)")
    
    # 动态DNS/临时域名
    temp_patterns = ['.shopjp.pw', '.gotdns.ch', '.anliyun.cc', '.globalnet.nic', '.wbsubdomain']
    temp_domains = [d for d in domains if d and any(pattern in d for pattern in temp_patterns)]
    print(f"   动态DNS/临时域名: {len(temp_domains):,} ({len(temp_domains)/len(sites)*100:.1f}%)")
    
    # 教育机构
    edu_domains = [d for d in domains if d and ('.edu' in d or '.ac.' in d)]
    print(f"   教育机构: {len(edu_domains):,} ({len(edu_domains)/len(sites)*100:.1f}%)")
    
    # 中国域名
    cn_domains = [d for d in domains if d and ('.cn' in d or '.com.cn' in d)]
    print(f"   中国域名: {len(cn_domains):,} ({len(cn_domains)/len(sites)*100:.1f}%)")
    print()
    
    # 分析可能的高价值目标
    print("🎯 可能的高价值目标:")
    
    # 企业域名（非IP，非临时域名）
    enterprise_domains = []
    for d in domains:
        if (d and 
            not ip_pattern.match(d) and 
            not any(pattern in d for pattern in local_patterns) and
            not any(pattern in d for pattern in temp_patterns) and
            '.' in d and
            len(d.split('.')) >= 2):
            enterprise_domains.append(d)
    
    print(f"   企业级域名: {len(enterprise_domains):,} ({len(enterprise_domains)/len(sites)*100:.1f}%)")
    
    # 标准端口（80, 443）
    standard_port_sites = [s for s in sites if ':80' in s or ':443' in s or (not ':' in s.split('//')[1])]
    print(f"   标准端口部署: {len(standard_port_sites):,} ({len(standard_port_sites)/len(sites)*100:.1f}%)")
    print()
    
    # 保存分析结果
    print("💾 保存分析结果...")
    
    # 保存详细统计
    with open('site_analysis.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['网站', '协议', '域名', '端口', '顶级域名', '类型'])
        
        for site in sites:
            try:
                parsed = urlparse(site)
                hostname = parsed.hostname or parsed.netloc.split(':')[0]
                port = parsed.port or (443 if parsed.scheme == 'https' else 80)
                tld = hostname.split('.')[-1] if hostname and '.' in hostname else ''
                
                # 判断类型
                site_type = 'unknown'
                if hostname:
                    if ip_pattern.match(hostname):
                        site_type = 'ip'
                    elif any(pattern in hostname for pattern in local_patterns):
                        site_type = 'local'
                    elif any(pattern in hostname for pattern in temp_patterns):
                        site_type = 'temp'
                    elif '.edu' in hostname or '.ac.' in hostname:
                        site_type = 'edu'
                    elif '.cn' in hostname:
                        site_type = 'cn'
                    else:
                        site_type = 'enterprise'
                
                writer.writerow([site, parsed.scheme, hostname, port, tld, site_type])
            
            except Exception:
                writer.writerow([site, '', '', '', '', 'error'])
    
    # 保存高价值目标
    with open('high_value_targets.txt', 'w', encoding='utf-8') as f:
        f.write("高价值 Open WebUI 部署目标\n")
        f.write("=" * 40 + "\n\n")
        f.write("企业级域名 (标准端口):\n")
        f.write("-" * 20 + "\n")
        
        enterprise_standard = []
        for site in sites:
            try:
                parsed = urlparse(site)
                hostname = parsed.hostname or parsed.netloc.split(':')[0]
                port = parsed.port or (443 if parsed.scheme == 'https' else 80)
                
                if (hostname and 
                    not ip_pattern.match(hostname) and 
                    not any(pattern in hostname for pattern in local_patterns) and
                    not any(pattern in hostname for pattern in temp_patterns) and
                    port in [80, 443] and
                    '.' in hostname):
                    enterprise_standard.append(site)
            except:
                continue
        
        for site in sorted(enterprise_standard)[:100]:  # 只显示前100个
            f.write(f"{site}\n")
        
        f.write(f"\n总计: {len(enterprise_standard)} 个企业级标准端口部署\n")
    
    print("✅ 分析完成！")
    print("📁 生成文件:")
    print("   - site_analysis.csv (详细分析)")
    print("   - high_value_targets.txt (高价值目标)")
    print("=" * 50)

if __name__ == "__main__":
    analyze_sites()
