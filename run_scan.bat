@echo off
chcp 65001 >nul
echo ========================================
echo Open WebUI 安全扫描工具
echo 作者：Claude 4.0 Sonnet
echo ========================================
echo.

echo 正在检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到 Python，请先安装 Python 3.7+
    pause
    exit /b 1
)

echo 正在检查依赖包...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 选择扫描模式：
echo 1. 演示版扫描（快速测试，约5个查询）
echo 2. 简化版扫描（全面扫描，约90个查询，推荐）
echo 3. 完整版扫描（功能最全，包含浏览器自动化）
echo.
set /p choice="请输入选择 (1, 2 或 3): "

if "%choice%"=="1" (
    echo.
    echo 开始运行演示版扫描...
    echo 注意：这将执行约5个FOFA查询，用于快速验证功能
    python demo_scanner.py
) else if "%choice%"=="2" (
    echo.
    echo 开始运行简化版全面扫描...
    echo 注意：这将执行约90个FOFA查询，预计需要15-30分钟
    echo 将搜索全球所有部署的Open WebUI网站并测试注册功能
    python simple_scanner.py
) else if "%choice%"=="3" (
    echo.
    echo 开始运行完整版扫描...
    echo 注意：包含浏览器自动化，需要更多时间和资源
    python fofa_scanner.py
) else (
    echo 无效选择，默认使用简化版扫描...
    python simple_scanner.py
)

echo.
echo 扫描完成！请查看生成的结果文件。
pause
