#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Open WebUI 扫描器
专注于 FOFA 搜索和基础检测
作者：Claude 4.0 Sonnet
"""

import requests
import base64
import json
import time
import random
from urllib.parse import urlparse, urljoin
import csv
from typing import List, Dict, Set
import logging
import re
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_scanner.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        logger.error("配置文件 config.json 不存在")
        return None
    except json.JSONDecodeError:
        logger.error("配置文件格式错误")
        return None

class SimpleFOFAScanner:
    """简化版 FOFA 搜索器"""

    def __init__(self, config: dict):
        fofa_config = config['fofa']
        self.fofa_url = fofa_config['url'].rstrip('/')
        self.api_key = fofa_config['api_key']
        self.max_results = fofa_config['max_results_per_query']
        self.delay_min = fofa_config['request_delay_min']
        self.delay_max = fofa_config['request_delay_max']

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': config['scan_settings']['user_agent']
        })
    
    def search(self, query: str, size: int = None) -> List[Dict]:
        """执行 FOFA 搜索"""
        if size is None:
            size = self.max_results

        try:
            # Base64 编码查询语句
            query_encoded = base64.b64encode(query.encode()).decode()

            # 构建 API 请求 URL
            api_url = f"{self.fofa_url}/api/v1/search/all"
            params = {
                'qbase64': query_encoded,
                'key': self.api_key,
                'size': size,
                'fields': 'host,ip,port,country,city,title'
            }
            
            logger.info(f"执行 FOFA 搜索: {query}")
            response = self.session.get(api_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('error'):
                logger.error(f"FOFA API 错误: {data['errmsg']}")
                return []
            
            results = data.get('results', [])
            logger.info(f"找到 {len(results)} 个结果")
            return results
            
        except Exception as e:
            logger.error(f"FOFA 搜索失败: {e}")
            return []
    
    def comprehensive_search(self) -> Set[str]:
        """全面搜索所有部署了 open-webui 的网站"""

        # 优化的查询列表，确保覆盖全面且每个查询 ≤ 10000 结果
        queries = [
            # 中国地区细分（按省份和城市）
            'title="open webui" && country="CN" && region="Beijing"',
            'title="open webui" && country="CN" && region="Shanghai"',
            'title="open webui" && country="CN" && region="Guangdong" && city!="Guangzhou" && city!="Shenzhen"',
            'title="open webui" && country="CN" && region="Guangdong" && (city="Guangzhou" || city="Shenzhen")',
            'title="open webui" && country="CN" && region="Zhejiang"',
            'title="open webui" && country="CN" && region="Jiangsu"',
            'title="open webui" && country="CN" && region="Sichuan"',
            'title="open webui" && country="CN" && region="Hubei"',
            'title="open webui" && country="CN" && region="Fujian"',
            'title="open webui" && country="CN" && region="Hunan"',
            'title="open webui" && country="CN" && region!="Beijing" && region!="Shanghai" && region!="Guangdong" && region!="Zhejiang" && region!="Jiangsu" && region!="Sichuan" && region!="Hubei" && region!="Fujian" && region!="Hunan"',

            # 美国地区细分（按州和主要城市）
            'title="open webui" && country="US" && region="California" && city!="Los Angeles" && city!="San Francisco"',
            'title="open webui" && country="US" && region="California" && (city="Los Angeles" || city="San Francisco")',
            'title="open webui" && country="US" && region="New York"',
            'title="open webui" && country="US" && region="Texas"',
            'title="open webui" && country="US" && region="Florida"',
            'title="open webui" && country="US" && region="Illinois"',
            'title="open webui" && country="US" && region="Washington"',
            'title="open webui" && country="US" && region!="California" && region!="New York" && region!="Texas" && region!="Florida" && region!="Illinois" && region!="Washington"',

            # 德国细分（按州/地区）
            'title="open webui" && country="DE" && region="North Rhine-Westphalia"',
            'title="open webui" && country="DE" && region="Bavaria"',
            'title="open webui" && country="DE" && region="Baden-Württemberg"',
            'title="open webui" && country="DE" && region="Hesse"',
            'title="open webui" && country="DE" && region="Berlin"',
            'title="open webui" && country="DE" && region!="North Rhine-Westphalia" && region!="Bavaria" && region!="Baden-Württemberg" && region!="Hesse" && region!="Berlin"',

            # 欧洲其他主要国家
            'title="open webui" && country="GB" && region!="England"',
            'title="open webui" && country="GB" && region="England" && city!="London"',
            'title="open webui" && country="GB" && region="England" && city="London"',
            'title="open webui" && country="FR" && region!="Île-de-France"',
            'title="open webui" && country="FR" && region="Île-de-France"',
            'title="open webui" && country="NL"',
            'title="open webui" && country="IT"',
            'title="open webui" && country="ES"',
            'title="open webui" && country="CH"',  # 瑞士
            'title="open webui" && country="AT"',  # 奥地利
            'title="open webui" && country="SE"',  # 瑞典
            'title="open webui" && country="NO"',  # 挪威
            'title="open webui" && country="DK"',  # 丹麦
            'title="open webui" && country="FI"',  # 芬兰
            'title="open webui" && country="PL"',  # 波兰

            # 亚洲国家细分
            'title="open webui" && country="JP" && region!="Tokyo"',
            'title="open webui" && country="JP" && region="Tokyo"',
            'title="open webui" && country="KR"',
            'title="open webui" && country="IN" && region!="Maharashtra" && region!="Karnataka"',
            'title="open webui" && country="IN" && (region="Maharashtra" || region="Karnataka")',
            'title="open webui" && country="SG"',
            'title="open webui" && country="HK"',
            'title="open webui" && country="TW"',  # 台湾
            'title="open webui" && country="TH"',  # 泰国
            'title="open webui" && country="MY"',  # 马来西亚
            'title="open webui" && country="ID"',  # 印尼
            'title="open webui" && country="VN"',  # 越南
            'title="open webui" && country="PH"',  # 菲律宾

            # 其他重要地区
            'title="open webui" && country="CA" && region!="Ontario"',
            'title="open webui" && country="CA" && region="Ontario"',
            'title="open webui" && country="AU" && region!="New South Wales"',
            'title="open webui" && country="AU" && region="New South Wales"',
            'title="open webui" && country="RU" && region!="Moscow"',
            'title="open webui" && country="RU" && region="Moscow"',
            'title="open webui" && country="BR"',
            'title="open webui" && country="MX"',  # 墨西哥
            'title="open webui" && country="AR"',  # 阿根廷
            'title="open webui" && country="CL"',  # 智利
            'title="open webui" && country="CO"',  # 哥伦比亚
            'title="open webui" && country="ZA"',  # 南非
            'title="open webui" && country="EG"',  # 埃及
            'title="open webui" && country="IL"',  # 以色列
            'title="open webui" && country="TR"',  # 土耳其
            'title="open webui" && country="AE"',  # 阿联酋
            'title="open webui" && country="SA"',  # 沙特

            # 按端口分类（细分443端口）
            'title="open webui" && port="443" && country="CN"',
            'title="open webui" && port="443" && country="US"',
            'title="open webui" && port="443" && country="DE"',
            'title="open webui" && port="443" && country!="CN" && country!="US" && country!="DE"',
            'title="open webui" && port="3000"',
            'title="open webui" && port="5000"',
            'title="open webui" && port="8080"',
            'title="open webui" && port="80"',
            'title="open webui" && port="8000"',
            'title="open webui" && port="9000"',
            'title="open webui" && port!="443" && port!="3000" && port!="5000" && port!="8080" && port!="80" && port!="8000" && port!="9000"',

            # 其他未覆盖的国家
            'title="open webui" && country!="CN" && country!="US" && country!="DE" && country!="GB" && country!="FR" && country!="NL" && country!="IT" && country!="ES" && country!="CH" && country!="AT" && country!="SE" && country!="NO" && country!="DK" && country!="FI" && country!="PL" && country!="JP" && country!="KR" && country!="IN" && country!="SG" && country!="HK" && country!="TW" && country!="TH" && country!="MY" && country!="ID" && country!="VN" && country!="PH" && country!="CA" && country!="AU" && country!="RU" && country!="BR" && country!="MX" && country!="AR" && country!="CL" && country!="CO" && country!="ZA" && country!="EG" && country!="IL" && country!="TR" && country!="AE" && country!="SA"',

            # 备用搜索（以防遗漏）
            'title="Open WebUI"',  # 大写版本
            'title="openwebui"',   # 无空格版本
            'title="OpenWebUI"',   # 驼峰命名
            'body="open-webui"',   # 在页面内容中搜索
            'body="openwebui"',    # 页面内容无连字符
            'header="open webui"', # 在HTTP头中搜索
        ]
        
        all_urls = set()
        
        for i, query in enumerate(queries, 1):
            logger.info(f"执行第 {i}/{len(queries)} 个查询")
            results = self.search(query)
            
            for result in results:
                if len(result) >= 1:  # 确保有 host 字段
                    host = result[0]
                    if host:
                        # 确保 URL 格式正确
                        if not host.startswith(('http://', 'https://')):
                            # 尝试两种协议
                            all_urls.add(f"https://{host}")
                            all_urls.add(f"http://{host}")
                        else:
                            all_urls.add(host)
            
            # 添加延迟避免请求过快
            time.sleep(random.uniform(self.delay_min, self.delay_max))
        
        logger.info(f"总共找到 {len(all_urls)} 个唯一网站")
        return all_urls

class SimpleWebTester:
    """简化版网站测试器"""

    def __init__(self, config: dict):
        scan_config = config['scan_settings']
        test_config = config['test_account']

        self.timeout = scan_config['timeout']
        self.max_retries = scan_config['max_retries']

        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': scan_config['user_agent']
        })
        self.test_email = test_config['email']
        self.test_password = test_config['password']
        self.test_username = test_config['username']
        self.test_name = test_config['name']
    
    def check_site_accessibility(self, url: str) -> Dict:
        """检查网站可访问性和基本信息"""
        result = {
            'url': url,
            'accessible': False,
            'has_signup': False,
            'title': '',
            'error': None
        }
        
        try:
            logger.info(f"检查网站: {url}")
            
            # 访问网站
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            response.raise_for_status()
            
            result['accessible'] = True
            content = response.text.lower()
            
            # 提取页面标题
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', response.text, re.IGNORECASE)
            if title_match:
                result['title'] = title_match.group(1).strip()
            
            # 检查是否有注册功能
            signup_indicators = [
                'sign up', 'signup', 'register', 'registration', '注册',
                'create account', 'join', 'get started'
            ]
            
            for indicator in signup_indicators:
                if indicator in content:
                    result['has_signup'] = True
                    break
            
            # 额外检查：查找注册相关的 URL 或按钮
            signup_patterns = [
                r'href=["\'][^"\']*signup[^"\']*["\']',
                r'href=["\'][^"\']*register[^"\']*["\']',
                r'action=["\'][^"\']*signup[^"\']*["\']',
                r'action=["\'][^"\']*register[^"\']*["\']'
            ]
            
            for pattern in signup_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    result['has_signup'] = True
                    break
            
        except requests.exceptions.Timeout:
            result['error'] = '连接超时'
        except requests.exceptions.ConnectionError:
            result['error'] = '连接失败'
        except requests.exceptions.HTTPError as e:
            result['error'] = f'HTTP错误: {e.response.status_code}'
        except Exception as e:
            result['error'] = f'未知错误: {str(e)}'
        
        return result
    
    def test_registration_endpoint(self, url: str) -> Dict:
        """测试注册端点"""
        result = {
            'url': url,
            'registration_possible': False,
            'api_endpoints': [],
            'error': None
        }
        
        try:
            # 常见的注册 API 端点
            endpoints_to_test = [
                '/api/v1/auths/signup',
                '/api/auth/signup', 
                '/auth/signup',
                '/signup',
                '/register',
                '/api/register',
                '/api/v1/register',
                '/api/users/register'
            ]
            
            for endpoint in endpoints_to_test:
                test_url = urljoin(url, endpoint)
                
                # 测试 POST 请求
                test_data = {
                    'email': self.test_email,
                    'password': self.test_password,
                    'username': self.test_username,
                    'name': self.test_name
                }
                
                try:
                    response = self.session.post(test_url, json=test_data, timeout=10)
                    
                    # 检查响应
                    if response.status_code in [200, 201, 400, 422]:  # 包括验证错误
                        result['api_endpoints'].append(endpoint)
                        
                        # 如果返回 200/201，可能注册成功
                        if response.status_code in [200, 201]:
                            result['registration_possible'] = True
                        
                        # 如果返回 400/422，检查错误信息
                        elif response.status_code in [400, 422]:
                            try:
                                error_data = response.json()
                                # 如果错误不是因为邮箱验证要求，则可能可以注册
                                error_text = str(error_data).lower()
                                if 'email' not in error_text or 'verification' not in error_text:
                                    result['registration_possible'] = True
                            except:
                                pass
                
                except requests.exceptions.RequestException:
                    continue  # 忽略单个端点的错误
                
                time.sleep(1)  # 避免请求过快
            
        except Exception as e:
            result['error'] = f'测试注册端点失败: {str(e)}'
        
        return result

    def test_actual_registration(self, url: str) -> Dict:
        """实际尝试注册测试"""
        result = {
            'url': url,
            'registration_successful': False,
            'registration_response': '',
            'models_found': [],
            'error': None
        }

        try:
            # 常见的注册 API 端点
            registration_endpoints = [
                '/api/v1/auths/signup',
                '/api/auth/signup',
                '/auth/signup',
                '/signup',
                '/register',
                '/api/register',
                '/api/v1/register',
                '/api/users/register'
            ]

            for endpoint in registration_endpoints:
                try:
                    reg_url = urljoin(url, endpoint)

                    # 准备注册数据
                    registration_data = {
                        'email': self.test_email,
                        'password': self.test_password,
                        'username': self.test_username,
                        'name': self.test_name
                    }

                    # 尝试注册
                    response = self.session.post(
                        reg_url,
                        json=registration_data,
                        timeout=self.timeout,
                        headers={'Content-Type': 'application/json'}
                    )

                    result['registration_response'] = f"Status: {response.status_code}, Response: {response.text[:200]}"

                    # 检查注册是否成功
                    if response.status_code in [200, 201]:
                        result['registration_successful'] = True
                        logger.info(f"✓ 注册成功: {url} - 端点: {endpoint}")

                        # 尝试提取模型信息
                        models = self._extract_models_from_response(response.text)
                        if models:
                            result['models_found'] = models

                        break

                    elif response.status_code in [400, 422]:
                        # 检查是否是因为邮箱已存在等可接受的错误
                        response_text = response.text.lower()
                        if any(keyword in response_text for keyword in ['already exists', 'already registered', 'duplicate']):
                            result['registration_successful'] = True
                            logger.info(f"✓ 注册功能可用（邮箱已存在）: {url} - 端点: {endpoint}")
                            break

                except requests.exceptions.RequestException:
                    continue  # 尝试下一个端点

                time.sleep(1)  # 避免请求过快

        except Exception as e:
            result['error'] = f'注册测试失败: {str(e)}'

        return result

    def _extract_models_from_response(self, response_text: str) -> List[str]:
        """从响应中提取模型信息"""
        models = []
        try:
            # 尝试解析JSON响应
            if response_text.strip().startswith('{'):
                data = json.loads(response_text)
                # 查找模型相关字段
                model_fields = ['models', 'available_models', 'model_list', 'llm_models']
                for field in model_fields:
                    if field in data and isinstance(data[field], list):
                        models.extend(data[field])

            # 使用正则表达式查找模型名称
            model_patterns = [
                r'gpt-[0-9a-zA-Z\-\.]+',
                r'claude-[0-9a-zA-Z\-\.]+',
                r'llama[0-9a-zA-Z\-\.]*',
                r'gemini[0-9a-zA-Z\-\.]*',
                r'mistral[0-9a-zA-Z\-\.]*',
                r'qwen[0-9a-zA-Z\-\.]*'
            ]

            for pattern in model_patterns:
                matches = re.findall(pattern, response_text, re.IGNORECASE)
                models.extend(matches)

        except Exception:
            pass

        return list(set(models))  # 去重

    def test_actual_registration(self, url: str) -> Dict:
        """实际尝试注册测试"""
        result = {
            'url': url,
            'registration_successful': False,
            'registration_response': '',
            'models_found': [],
            'error': None
        }

        try:
            # 常见的注册 API 端点
            registration_endpoints = [
                '/api/v1/auths/signup',
                '/api/auth/signup',
                '/auth/signup',
                '/signup',
                '/register',
                '/api/register',
                '/api/v1/register',
                '/api/users/register',
                '/api/v1/users/signup'
            ]

            for endpoint in registration_endpoints:
                try:
                    reg_url = urljoin(url, endpoint)

                    # 准备注册数据
                    registration_data = {
                        'email': self.test_email,
                        'password': self.test_password,
                        'username': self.test_username,
                        'name': self.test_name
                    }

                    # 尝试注册
                    response = self.session.post(
                        reg_url,
                        json=registration_data,
                        timeout=self.timeout,
                        headers={'Content-Type': 'application/json'}
                    )

                    result['registration_response'] = f"Status: {response.status_code}, Response: {response.text[:200]}"

                    # 检查注册是否成功
                    if response.status_code in [200, 201]:
                        result['registration_successful'] = True
                        logger.info(f"✓ 注册成功: {url} - 端点: {endpoint}")

                        # 尝试提取模型信息
                        models = self._extract_models_from_response(response.text)
                        if models:
                            result['models_found'] = models

                        # 尝试登录获取更多信息
                        login_models = self._try_login_and_get_models(url)
                        if login_models:
                            result['models_found'].extend(login_models)
                            result['models_found'] = list(set(result['models_found']))  # 去重

                        break

                    elif response.status_code in [400, 422]:
                        # 检查是否是因为邮箱已存在等可接受的错误
                        response_text = response.text.lower()
                        if any(keyword in response_text for keyword in ['already exists', 'already registered', 'duplicate']):
                            result['registration_successful'] = True
                            logger.info(f"✓ 注册功能可用（邮箱已存在）: {url} - 端点: {endpoint}")
                            break

                except requests.exceptions.RequestException:
                    continue  # 尝试下一个端点

                time.sleep(1)  # 避免请求过快

        except Exception as e:
            result['error'] = f'注册测试失败: {str(e)}'

        return result

    def _extract_models_from_response(self, response_text: str) -> List[str]:
        """从响应中提取模型信息"""
        models = []
        try:
            # 尝试解析JSON响应
            if response_text.strip().startswith('{'):
                data = json.loads(response_text)
                # 查找模型相关字段
                model_fields = ['models', 'available_models', 'model_list', 'llm_models']
                for field in model_fields:
                    if field in data and isinstance(data[field], list):
                        models.extend(data[field])

            # 使用正则表达式查找模型名称
            model_patterns = [
                r'gpt-[0-9a-zA-Z\-\.]+',
                r'claude-[0-9a-zA-Z\-\.]+',
                r'llama[0-9a-zA-Z\-\.]*',
                r'gemini[0-9a-zA-Z\-\.]*',
                r'mistral[0-9a-zA-Z\-\.]*',
                r'qwen[0-9a-zA-Z\-\.]*'
            ]

            for pattern in model_patterns:
                matches = re.findall(pattern, response_text, re.IGNORECASE)
                models.extend(matches)

        except Exception:
            pass

        return list(set(models))  # 去重

    def _try_login_and_get_models(self, url: str) -> List[str]:
        """尝试登录并获取模型列表"""
        models = []
        try:
            # 常见的登录端点
            login_endpoints = [
                '/api/v1/auths/signin',
                '/api/auth/signin',
                '/auth/signin',
                '/login',
                '/api/login',
                '/api/v1/login'
            ]

            login_data = {
                'email': self.test_email,
                'password': self.test_password
            }

            for endpoint in login_endpoints:
                try:
                    login_url = urljoin(url, endpoint)
                    response = self.session.post(
                        login_url,
                        json=login_data,
                        timeout=self.timeout,
                        headers={'Content-Type': 'application/json'}
                    )

                    if response.status_code == 200:
                        # 登录成功，尝试获取模型列表
                        models_endpoints = [
                            '/api/models',
                            '/api/v1/models',
                            '/models',
                            '/api/ollama/models',
                            '/api/openai/models'
                        ]

                        for models_endpoint in models_endpoints:
                            try:
                                models_url = urljoin(url, models_endpoint)
                                models_response = self.session.get(models_url, timeout=self.timeout)

                                if models_response.status_code == 200:
                                    extracted_models = self._extract_models_from_response(models_response.text)
                                    models.extend(extracted_models)

                            except:
                                continue

                        break

                except:
                    continue

        except Exception:
            pass

        return list(set(models))  # 去重

def main():
    """主函数"""
    logger.info("开始简化版 Open WebUI 扫描")

    # 加载配置
    config = load_config()
    if not config:
        logger.error("无法加载配置文件，程序退出")
        return

    # 初始化扫描器
    fofa_scanner = SimpleFOFAScanner(config)
    web_tester = SimpleWebTester(config)
    
    try:
        # 1. 搜索所有网站
        logger.info("步骤 1: 搜索部署了 Open WebUI 的网站")
        all_urls = fofa_scanner.comprehensive_search()
        
        # 保存所有找到的 URL
        with open('all_openwebui_sites.txt', 'w', encoding='utf-8') as f:
            for url in sorted(all_urls):
                f.write(f"{url}\n")
        
        logger.info(f"已保存 {len(all_urls)} 个网站到 all_openwebui_sites.txt")
        
        # 2. 检查网站可访问性
        logger.info("步骤 2: 检查网站可访问性和注册功能")
        accessible_sites = []
        potential_vulnerable_sites = []
        
        for i, url in enumerate(all_urls, 1):
            logger.info(f"检查进度: {i}/{len(all_urls)}")
            
            # 基础检查
            basic_result = web_tester.check_site_accessibility(url)
            
            if basic_result['accessible']:
                accessible_sites.append(basic_result)
                
                if basic_result['has_signup']:
                    # 进一步测试注册功能
                    reg_result = web_tester.test_registration_endpoint(url)
                    if reg_result['registration_possible'] or reg_result['api_endpoints']:
                        # 实际尝试注册
                        actual_reg_result = web_tester.test_actual_registration(url)

                        site_info = {
                            'url': url,
                            'title': basic_result['title'],
                            'has_signup_ui': basic_result['has_signup'],
                            'api_endpoints': reg_result['api_endpoints'],
                            'registration_possible': reg_result['registration_possible'],
                            'actual_registration_successful': actual_reg_result['registration_successful'],
                            'models_found': actual_reg_result['models_found'],
                            'registration_response': actual_reg_result['registration_response']
                        }

                        potential_vulnerable_sites.append(site_info)

                        if actual_reg_result['registration_successful']:
                            logger.info(f"🚨 发现可实际注册网站: {url}")
                            if actual_reg_result['models_found']:
                                logger.info(f"   发现模型: {', '.join(actual_reg_result['models_found'])}")
                        else:
                            logger.info(f"发现潜在可注册网站: {url}")
            
            # 添加延迟
            time.sleep(random.uniform(2, 4))
        
        # 3. 保存结果
        logger.info("步骤 3: 保存扫描结果")
        
        # 保存可访问的网站
        with open('accessible_sites.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['URL', '标题', '有注册功能', '错误信息'])
            
            for site in accessible_sites:
                writer.writerow([
                    site['url'], 
                    site['title'], 
                    '是' if site['has_signup'] else '否',
                    site['error'] or ''
                ])
        
        # 保存潜在可注册的网站
        with open('potential_vulnerable_sites.txt', 'w', encoding='utf-8') as f:
            f.write("潜在可免费注册的 Open WebUI 网站\n")
            f.write("=" * 50 + "\n\n")

            for site in potential_vulnerable_sites:
                f.write(f"网站: {site['url']}\n")
                f.write(f"标题: {site['title']}\n")
                f.write(f"有注册界面: {'是' if site['has_signup_ui'] else '否'}\n")
                f.write(f"发现的API端点: {', '.join(site['api_endpoints'])}\n")
                f.write(f"可能可以注册: {'是' if site['registration_possible'] else '否'}\n")
                f.write(f"实际注册成功: {'是' if site['actual_registration_successful'] else '否'}\n")
                f.write(f"发现的模型: {', '.join(site['models_found']) if site['models_found'] else '无'}\n")
                f.write(f"注册响应: {site['registration_response']}\n")
                f.write("-" * 30 + "\n")

        # 保存实际可注册的网站（重点关注）
        actually_vulnerable = [site for site in potential_vulnerable_sites if site['actual_registration_successful']]

        with open('actually_vulnerable_sites.txt', 'w', encoding='utf-8') as f:
            f.write("实际可免费注册的 Open WebUI 网站及其模型\n")
            f.write("=" * 60 + "\n\n")

            for site in actually_vulnerable:
                f.write(f"🚨 网站: {site['url']}\n")
                f.write(f"   标题: {site['title']}\n")
                f.write(f"   可用模型: {', '.join(site['models_found']) if site['models_found'] else '未检测到模型'}\n")
                f.write(f"   注册端点: {', '.join(site['api_endpoints'])}\n")
                f.write("-" * 40 + "\n")

        # 保存CSV格式（包含模型信息）
        with open('vulnerable_sites_with_models.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['网站URL', '标题', '实际可注册', '可用模型', 'API端点', '注册响应'])

            for site in potential_vulnerable_sites:
                writer.writerow([
                    site['url'],
                    site['title'],
                    '是' if site['actual_registration_successful'] else '否',
                    ', '.join(site['models_found']) if site['models_found'] else '无',
                    ', '.join(site['api_endpoints']),
                    site['registration_response'][:100] + '...' if len(site['registration_response']) > 100 else site['registration_response']
                ])
        
        # 统计结果
        actually_vulnerable_count = len([site for site in potential_vulnerable_sites if site['actual_registration_successful']])
        sites_with_models = len([site for site in potential_vulnerable_sites if site['models_found']])

        logger.info("=" * 60)
        logger.info("🎉 全面扫描完成！")
        logger.info(f"📊 总共找到 {len(all_urls)} 个 Open WebUI 网站")
        logger.info(f"🌐 其中 {len(accessible_sites)} 个可访问")
        logger.info(f"⚠️  发现 {len(potential_vulnerable_sites)} 个潜在可注册网站")
        logger.info(f"🚨 实际可注册: {actually_vulnerable_count} 个")
        logger.info(f"🤖 发现模型信息: {sites_with_models} 个网站")
        logger.info("📁 详细结果已保存到以下文件:")
        logger.info("   - all_openwebui_sites.txt (所有网站)")
        logger.info("   - accessible_sites.csv (可访问网站)")
        logger.info("   - potential_vulnerable_sites.txt (潜在可注册)")
        logger.info("   - actually_vulnerable_sites.txt (实际可注册)")
        logger.info("   - vulnerable_sites_with_models.csv (包含模型信息)")
        logger.info("=" * 60)
        
    except KeyboardInterrupt:
        logger.info("用户中断扫描")
    except Exception as e:
        logger.error(f"扫描过程中发生错误: {e}")

if __name__ == "__main__":
    main()
