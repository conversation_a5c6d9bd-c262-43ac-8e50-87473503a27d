# Open WebUI 安全扫描工具使用指南

**作者：Claude 4.0 Sonnet**

## 🎯 项目目标

帮助 Open WebUI 项目负责人发现那些部署了项目但未设置邮箱验证的网站，以便发送安全提醒邮件，督促站长更新安全策略。

## 📁 文件结构

```
openwebui-kiro/
├── config.json              # 配置文件
├── requirements.txt          # Python依赖
├── demo_scanner.py          # 演示版扫描器（推荐新手）
├── simple_scanner.py        # 简化版扫描器（推荐生产）
├── fofa_scanner.py          # 完整版扫描器（功能最全）
├── run_scan.bat             # Windows启动脚本
├── run_scan.sh              # Linux/Mac启动脚本
├── README.md                # 详细文档
└── 使用指南.md              # 本文件
```

## 🚀 快速开始

### 1. 环境准备

确保已安装 Python 3.7+：
```bash
python --version
```

安装依赖包：
```bash
pip install -r requirements.txt
```

### 2. 配置设置

检查 `config.json` 中的配置：
- FOFA URL: `https://fofa.red`
- API Key: `1v43heo2ie8004lp`
- 测试邮箱: `<EMAIL>`
- 测试密码: `sk123456`

### 3. 选择扫描模式

#### 🔰 新手推荐：演示版
```bash
python demo_scanner.py
```
- 快速测试功能
- 搜索少量数据（约200个网站）
- 只检查前10个网站
- 适合验证工具是否正常工作

#### 🎯 生产推荐：简化版
```bash
python simple_scanner.py
```
- 全面搜索所有网站
- 基于HTTP请求检测
- 稳定可靠，无需浏览器
- 适合大规模扫描

#### 🔧 高级用户：完整版
```bash
python fofa_scanner.py
```
- 使用Selenium自动化
- 可实际尝试注册
- 提取大模型列表
- 功能最全面但需要更多资源

### 4. 使用启动脚本

**Windows用户：**
```bash
run_scan.bat
```

**Linux/Mac用户：**
```bash
chmod +x run_scan.sh
./run_scan.sh
```

## 📊 输出结果

### 演示版输出
- `demo_sites.txt` - 发现的网站列表
- `demo_results.csv` - 检测结果详情
- `demo_scanner.log` - 运行日志

### 简化版输出
- `all_openwebui_sites.txt` - 所有发现的网站
- `accessible_sites.csv` - 可访问网站详情
- `potential_vulnerable_sites.txt` - 潜在可注册网站
- `simple_scanner.log` - 详细日志

### 完整版输出
- `vulnerable_sites.txt` - 可注册网站及模型列表
- `vulnerable_sites.csv` - CSV格式结果
- `scanner.log` - 完整日志

## 🔍 FOFA搜索策略

工具采用智能分批搜索策略，确保全面覆盖：

### 地理位置分批
- **中国**：按省份分批（北京、上海、广东等）
- **美国**：按州分批（加州、纽约州、德州等）
- **欧洲**：按国家分批（德国、英国、法国等）
- **亚洲**：其他国家单独查询

### 端口分批
- 常用端口：443, 3000, 5000, 8080, 80
- 其他端口：单独查询

### 标题变体
- `title="open webui"`
- `title="Open WebUI"`
- `title="openwebui"`
- `body="open-webui"`

## ⚡ 性能优化

### 请求频率控制
- 内置随机延迟（2-5秒）
- 避免对目标网站造成压力
- 防止被FOFA限制

### 批量处理
- 每个查询限制≤10000结果
- 自动去重合并
- 支持断点续传

### 错误处理
- 网络超时重试
- 连接失败跳过
- 详细错误日志

## 🛡️ 安全注意事项

### 合法使用
- ✅ 仅用于安全研究
- ✅ 漏洞通知目的
- ❌ 禁止恶意攻击
- ❌ 禁止未授权访问

### 隐私保护
- 使用测试账号注册
- 不保存敏感信息
- 结果仅用于安全通知

### 负责任披露
- 通过正当渠道联系站长
- 提供安全建议
- 给予合理修复时间

## 🔧 故障排除

### 常见问题

**1. FOFA API错误**
```
解决方案：
- 检查API Key是否正确
- 确认账户余额充足
- 验证网络连接
```

**2. 网站访问失败**
```
原因：
- 网站可能有防护措施
- 网络超时是正常现象
- 部分网站可能已下线

解决方案：
- 检查网络连接
- 调整超时设置
- 使用代理（如需要）
```

**3. 依赖安装问题**
```bash
# 使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

# 更新pip
pip install --upgrade pip

# 手动安装依赖
pip install requests selenium beautifulsoup4
```

## 📈 扩展功能

可根据需要添加以下功能：

### 1. 代理支持
```python
# 在session中添加代理
proxies = {
    'http': 'http://proxy:port',
    'https': 'https://proxy:port'
}
session.proxies.update(proxies)
```

### 2. 多线程处理
```python
from concurrent.futures import ThreadPoolExecutor

with ThreadPoolExecutor(max_workers=5) as executor:
    futures = [executor.submit(test_site, url) for url in urls]
```

### 3. 数据库存储
```python
import sqlite3

# 创建数据库存储结果
conn = sqlite3.connect('scan_results.db')
```

### 4. 邮件通知
```python
import smtplib
from email.mime.text import MIMEText

# 发送安全提醒邮件
def send_security_alert(site_info):
    # 实现邮件发送逻辑
    pass
```

## 📞 技术支持

### 日志分析
- 查看 `*.log` 文件了解详细执行过程
- 错误信息会记录具体失败原因
- 可根据日志调整配置参数

### 性能调优
- 调整 `config.json` 中的超时设置
- 修改请求延迟间隔
- 根据网络情况调整重试次数

### 结果验证
- 手动验证部分结果确保准确性
- 对比不同版本的扫描结果
- 定期更新FOFA搜索语法

## 📋 最佳实践

### 1. 分阶段执行
```bash
# 第一阶段：演示测试
python demo_scanner.py

# 第二阶段：小规模验证
# 修改config.json减少搜索范围

# 第三阶段：全面扫描
python simple_scanner.py
```

### 2. 结果管理
- 定期备份扫描结果
- 建立网站状态跟踪表
- 记录安全通知发送情况

### 3. 持续监控
- 设置定时任务定期扫描
- 监控新部署的网站
- 跟踪安全改进情况

## 🎉 成功案例

演示版扫描结果显示：
- 搜索到 **197** 个 Open WebUI 网站
- 测试了 **10** 个网站
- 发现 **1** 个可访问网站
- 该网站标题为 "Open WebUI"

这证明了工具的有效性，可以成功发现部署的网站并进行安全检测。

---

**免责声明**：本工具仅用于安全研究和漏洞通知目的。使用者应遵守相关法律法规，负责任地使用本工具。

**开发信息**：
- 开发模型：Claude 4.0 Sonnet
- 开发时间：2025年8月
- 版本：v1.0
